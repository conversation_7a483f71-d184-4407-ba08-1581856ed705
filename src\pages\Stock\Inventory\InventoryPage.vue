<template>
  <StockNavigation></StockNavigation>
  <div class="container q-pa-md q-ma-md page-fade-in">
    <div class="row q-col-gutter-md justify-between q-mb-lg">
      <!-- Card 1: สีเขียว -->
      <div class="col-12 col-sm-6 col-md-4">
        <q-card class="my-card green-card text-white flex flex-center items-center q-pa-md">
          <q-icon name="check_circle" size="60px" color="white" class="q-mr-sm" />
          <div class="column self-start">
            <div class="row">
              <span class="text-subtitle1">สินค้าคงอยู่</span>
            </div>
            <div class="row">
              <span class="text-h4 text-weight-bold">{{ stockStatusCounts.inStock }}</span>
            </div>
          </div>
        </q-card>
      </div>

      <!-- Card 2: สีเหลือง -->
      <div class="col-12 col-sm-6 col-md-4">
        <q-card class="my-card yellow-card text-white flex flex-center items-center q-pa-md">
          <q-icon name="warning" size="60px" color="white" class="q-mr-sm" />
          <div class="column self-start">
            <div class="row">
              <span class="text-subtitle1">สินค้าใกล้หมด</span>
            </div>
            <div class="row">
              <span class="text-h4 text-weight-bold">{{ stockStatusCounts.lowStock }}</span>
            </div>
          </div>
        </q-card>
      </div>

      <!-- Card 3: สีแดง -->
      <div class="col-12 col-sm-6 col-md-4">
        <q-card class="my-card red-card flex flex-center items-center q-pa-md">
          <q-icon name="cancel" size="60px" color="white" class="q-mr-sm" />
          <div class="column self-start">
            <div class="row">
              <span class="text-subtitle1">สินค้าหมด</span>
            </div>
            <div class="row">
              <span class="text-h4 text-weight-bold">{{ stockStatusCounts.outOfStock }}</span>
            </div>
          </div>
        </q-card>
      </div>
    </div>

    <!-- เพิ่ม q-mt-lg เพื่อให้มีช่องว่าง -->
    <div class="row wrap-container q-mt-lg">
      <div class="col-8 col-md-12 col-sm-12 col-xs-12 search-container" style="max-width: 700px; width: 80%">
        <SearchComponent v-model="store.searchText" placeholder="ค้นหา" />
      </div>
      <div class="col-2 filter-button-container">
        <FilterComponent v-model="store.selectedFilter" :filterOptions="filterOptions" />
      </div>
      <div class="col-2 filter-button-container">
        <BranchComponent v-model="store.selectedBranch" />
      </div>
    </div>

    <q-card flat class="custom-table q-mt-lg">
      <q-table class="body-table" :rows="store.getStocks" :columns="store.stockColumns" :pagination="pagination"
        :rows-per-page-options="[]" row-key="id" style="height: 100%; max-height: 700px">
        <template #body-cell-actions="props">
          <q-td class="q-gutter-x-sm" style="min-width: 100px">
            <q-btn icon="call_split" padding="none" flat @click="openBreakDialog(props.row)"
              :style="{ color: '#0B7189' }" />
            <q-btn icon="edit" padding="none" flat @click="openEditDialog(props.row)" :style="{ color: '#E19F62' }" />

            <q-btn icon="info" padding="none" flat @click="openInfoDialog(props.row)" :style="{ color: '#294888' }">
            </q-btn>
          </q-td>
        </template>
        <template #body-cell-status="props">
          <q-td class="q-gutter-x-sm" style="min-width: 100px; text-align: center">
            <q-badge :label="props.row.status" :class="{
              'green-card': props.row.status === 'สินค้าคงอยู่',
              'yellow-card': props.row.status === 'สินค้าใกล้หมด',
              'red-card': props.row.status === 'สินค้าหมด',
            }" class="q-ml-none" style="
                height: 30px;
                width: 100px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
              ">
            </q-badge>
          </q-td>
        </template>
      </q-table>
      <EditStockDialog />
      <InfoStockDialog v-model="infoStockDialogOpen" />
    </q-card>
  </div>
  <BreakStockDialog v-model="breakDialogOpen" :stock="selectedStock" @break-completed="onBreakCompleted" />
</template>
<script setup lang="ts">
import StockNavigation from '../components/StockNavigation.vue'
import SearchComponent from 'src/components/searchComponent.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import { useStockStore } from 'src/stores/inventory/stock'
import BranchComponent from 'src/components/BranchComponent.vue'
import { computed, onMounted, ref, watch } from 'vue'
import type { Stock } from 'src/pages/Stock/Inventory/types/stock'
import EditStockDialog from 'src/pages/Stock/Inventory/dialog/EditStockDialog.vue'
import InfoStockDialog from 'src/pages/Stock/Inventory/dialog/InfoStockDialog.vue'
import { useBranchStore } from 'src/stores/inventory/branch'
import { useDialogStockStore } from 'src/stores/dialogs/dialog-stock'
import BreakStockDialog from 'src/pages/Stock/Inventory/dialog/BreakStockDialog.vue'


const store = useStockStore()
const storeBranch = useBranchStore()
const dialogStore = useDialogStockStore()
const infoStockDialogOpen = ref(false)
const pagination = ref({
  rowsPerPage: 12,
})
watch(
  [() => store.searchText, () => store.selectedFilter, () => store.selectedBranch],
  async () => {
    await store.fetchAllStockByFilter()
  },
)

onMounted(async () => {
  await store.fetchAllStock()
  await storeBranch.fetchAllBranch()
  await store.fetchAllStockByFilter()
})
const openEditDialog = (row: Stock) => {
  store.form = { ...row }
  dialogStore.open('edit')
  console.log(dialogStore.isOpen)
}
async function openInfoDialog(row: Stock) {
  store.form = { ...row }
  await store.fetchAllStock()
  infoStockDialogOpen.value = true
}
const stockStatusCounts = computed(() => {
  return store.stocks.reduce(
    (counts, stock) => {
      if (stock.remaining === 0) {
        counts.outOfStock++ // สินค้าหมด
      } else if (stock.remaining < stock.product.stock_min) {
        counts.lowStock++ // สินค้าใกล้หมด
      } else {
        counts.inStock++ // สินค้าคงอยู่
      }
      return counts
    },
    { inStock: 0, lowStock: 0, outOfStock: 0 },
  )
})
const filterOptions = [
  { label: 'บาร์โค้ด', value: 'barcode' },
  { label: 'รหัสสินค้า', value: 'product_code' },
  { label: 'ชื่อสินค้า', value: 'product_name' },
  { label: 'สถานที่เก็บ', value: 'storage_location' },
  { label: 'กลุ่มชื่อสามัญ', value: 'generic_group' },
  { label: 'ข้อความเตือน', value: 'warning' },
]

const breakDialogOpen = ref(false)
const selectedStock = ref<Stock | null>(null)

const openBreakDialog = (row: Stock) => {
  selectedStock.value = { ...row }
  breakDialogOpen.value = true
}

const onBreakCompleted = async () => {
  await store.fetchAllStock()
  await store.fetchAllStockByFilter()
}
</script>
<style scoped>
.my-card {
  min-height: 120px;
}

@media (max-width: 600px) {
  .my-card {
    flex-direction: column;
    /* เปลี่ยนเป็นแนวตั้ง */
    padding: 10px;
  }

  .q-icon {
    size: 40px;
    margin-bottom: 5px;
  }
}

/* กำหนดสีที่แตกต่างกันสำหรับแต่ละ card */
.green-card {
  background-color: #439e62;
  /* สีเขียว */
  color: white;
}

.yellow-card {
  background-color: #ed9b53;
  /* สีเหลือง */
  color: white;
}

.red-card {
  background-color: #b53638;
  /* สีแดง */
  color: white;
}

/* กำหนดระยะห่างระหว่าง column โดยใช้ q-gutter-md */
.q-gutter-md {
  gap: 10px;
  /* กำหนดระยะห่างระหว่าง card */
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 311px;
  max-width: 1042px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}
</style>
