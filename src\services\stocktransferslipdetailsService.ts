import type { GoodsReceipt } from 'src/pages/Stock/GoodsReceive/types/goodsReceipt'
import type { GoodsReceiptDetail } from 'src/pages/Stock/GoodsReceive/types/goodsReceiptDatail'
import type { StockTransferSlipDetails } from 'src/types/stockTransferSlipDetails'
import { api } from 'src/boot/axios'

const API_URL = '/sts-details'
// เปลี่ยนให้ตรงกับ backend ของคุณ

export const stocktransferSlipDetailService = {
  async getAll(): Promise<StockTransferSlipDetails[]> {
    const response = await api.get(API_URL)
    return response.data
  },

  async getById(id: number): Promise<StockTransferSlipDetails> {
    const response = await api.get(`${API_URL}/${id}`)
    return response.data
  },

  async getDetailsById(productId: number, branchId: number): Promise<GoodsReceiptDetail> {
    const response = await api.get(`${API_URL}/product/${productId}/branch/${branchId}`)
    return response.data
  },

  async getDetailsBySTS(id: number): Promise<StockTransferSlipDetails[]> {
    const response = await api.get(`${API_URL}/sts/${id}`)
    console.log('fromresponse', response)
    return response.data
  },

  async addProduct(stsDetails: StockTransferSlipDetails[], stsId: number) {
    await api.post(`${API_URL}/${stsId}`, stsDetails)
  },

  // อัปเดต Purchase Order
  // async update(id: number, gr: Partial<GoodsReceipt>): Promise<GoodsReceipt> {
  //   const response = await api.put(`${API_URL}/${id}`, gr)
  //   return response.data
  // },

  async updateSTSDetailsFromSTO(stsDetails: StockTransferSlipDetails[], stsId: number) {
    await api.put(`${API_URL}/${stsId}/update-from-sto`, stsDetails)
  },

  // ลบ Purchase Order
  async delete(id: number): Promise<void> {
    await api.delete(`${API_URL}/${id}`)
  },

  async filter(
    search: string,
    filter: string,
    startDate: string,
    endDate: string,
  ): Promise<GoodsReceipt[]> {
    try {
      const response = await api.post(`${API_URL}/filter`, {
        search,
        filter,
        startDate,
        endDate,
      })
      return response.data
    } catch (error) {
      console.error('Error filtering purchase orders', error)
      throw error
    }
  },
}
