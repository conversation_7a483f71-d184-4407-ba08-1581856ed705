import { defineStore, acceptHMRUpdate } from 'pinia'
import type { Stock } from 'src/pages/Stock/Inventory/types/stock'
import { ref } from 'vue'
import type { StockTransferOrder } from 'src/types/stockTransferOrder'
import { StockTransferOrderService } from 'src/services/stocktransferorderService'
import type { StockTransferOrderDetails } from 'src/types/stockTransferOrderDetails'
import { StockTransferOrderDetailsService } from 'src/services/stocktransferordertdetailService'


export const useStockTransferOrderStore = defineStore('sto', {
  state: () => ({
    form: {} as StockTransferOrder,
    formSTODetails: {} as StockTransferOrderDetails,
    sto: [] as StockTransferOrder[],
    deletedIds: [] as number[],
    stoDetails: [] as StockTransferOrderDetails[],
    stoDetailsEdited: [] as StockTransferOrderDetails[],
    stoProcess: [] as StockTransferOrder[],
    stoPrepare: [] as StockTransferOrder[],
    searchText: ref(''),
    selectedFilter: ref(''),
    selectedBranch: ref(''),
    stocksDialog: [] as Stock[],
    searchTextDialog: ref(''),
    selectedFilterDialog: ref(''),
    selectedBranchDialog: ref(''),
  }),

  getters: {
    getSTO: (s) => s.sto,
  },

  actions: {
    async fetchAllSTO() {
      try {
        const data = await StockTransferOrderService.getAll()
        this.sto = data
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    },
    async fetchSTOByStatus() {
      try {
        this.stoPrepare = await StockTransferOrderService.getSTOByStatus('เตรียมรายการ')
        this.stoProcess = await StockTransferOrderService.getSTOByStatus('ดำเนินการ')
      } catch (error) {
        console.error('Error fetching gr:', error)
      }
    },
    async fetchSTOById(id: number) {
      try {
        const sto = await StockTransferOrderService.getSTOById(id)
        return (this.form = sto)
      } catch (error) {
        console.error(`Error fetching sto ${id}:`, error)
      }
    },
    async fetchDetailsBySTO(id: number) {
      try {
        const newDetails = await StockTransferOrderDetailsService.getDetailsBySTO(id)
        return (this.stoDetails = newDetails)
      } catch (error) {
        console.error(`Error fetching sto ${id}:`, error)
      }
    },
    async create() {
      try {
        const newSTO = await StockTransferOrderService.create(this.form)
        return (this.form = newSTO)
      } catch (error) {
        console.error('Error adding order:', error)
      }
    },
    async createSTODetails(stoDetails: StockTransferOrderDetails[], stoId: number) {
      try {
        await StockTransferOrderDetailsService.create(stoDetails, stoId)
      } catch (error) {
        console.error('Error adding order:', error)
      }
    },
    async update() {
      try {
        const data = await StockTransferOrderService.updateOne(this.form.id, this.form)
        if (data) {
          await this.fetchAllSTO()
        }
      } catch (error) {
        console.error('Error updating stock:', error)
      }
    },

    resetForm() {
      this.form = {} as StockTransferOrder
      this.stoDetails = [] as StockTransferOrderDetails[]
    },
    resetFormSTODetails() {
      this.formSTODetails = {} as StockTransferOrderDetails
    }
  },
})

// Hot Module Replacement (HMR)
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useStockTransferOrderStore, import.meta.hot))
}
