<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">แดชบอร์ดประสิทธิภาพการดำเนินงาน</div>

    <div class="row q-col-gutter-md q-mb-md">
      <!-- ผู้จำหน่ายที่ส่งของตรงเวลามากที่สุด -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section style="height: 420px" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">ผู้จำหน่ายที่ส่งของตรงเวลามากที่สุด</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="onTimeSuppliersChartRef"
                style="width: 100%; height: 100%; display: block"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- สินค้าที่ส่งล่าช้า -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section style="height: 420px" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">สินค้าที่ส่งล่าช้า</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="slowProductsChartRef"
                style="width: 100%; height: 100%; display: block"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- ประสิทธิภาพการส่งของของผู้จำหน่าย -->
    <div class="row">
      <div class="col-12">
        <q-card>
          <q-card-section style="height: 450px" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">ประสิทธิภาพการจัดส่งของผู้จำหน่าย</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="supplierEfficiencyChartRef"
                style="width: 100%; height: 100%; display: block"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { TooltipItem } from 'chart.js'
import { api } from 'src/boot/axios'

Chart.register(...registerables)

const onTimeSuppliersChartRef = ref<HTMLCanvasElement>()
const slowProductsChartRef = ref<HTMLCanvasElement>()
const supplierEfficiencyChartRef = ref<HTMLCanvasElement>()

interface OnTimeSupplier {
  supplier_name: string
  on_time_ratio: number
}

interface SlowProduct {
  product_name: string
  total_ordered: number
  avg_receiving_days: number
}

interface SupplierEfficiency {
  supplier_name: string
  avg_delivery_days: number
  efficiency_rating: string
}

// กราฟ 1: ผู้จำหน่ายที่ส่งของตรงเวลา
const createOnTimeSuppliersChart = async () => {
  try {
    const response = await api.get<OnTimeSupplier[]>('/dashboard/performance/top-on-time-suppliers')
    const data = response.data.slice(0, 10)

    new Chart(onTimeSuppliersChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.supplier_name),
        datasets: [
          {
            label: 'เปอร์เซ็นต์การจัดส่งตรงเวลา',
            data: data.map((item) => item.on_time_ratio),
            backgroundColor: '#4CAF50',
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        indexAxis: 'y',
        layout: {
          padding: {
            bottom: 40,
          },
        },
        scales: {
          x: { beginAtZero: true, max: 100 },
        },
        plugins: {
          legend: { position: 'top' },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างกราฟผู้จำหน่ายที่ส่งของตรงเวลาได้:', error)
  }
}

// กราฟ 2: สินค้าที่ส่งล่าช้า
const createSlowProductsChart = async () => {
  try {
    const response = await api.get<SlowProduct[]>('/dashboard/performance/slow-products')
    const data = response.data.slice(0, 10)

    new Chart(slowProductsChartRef.value!, {
      type: 'scatter',
      data: {
        datasets: [
          {
            label: 'สินค้า',
            data: data.map((item) => ({
              x: item.total_ordered,
              y: item.avg_receiving_days,
              label: item.product_name,
            })),
            backgroundColor: '#F44336',
            borderColor: '#D32F2F',
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            bottom: 40,
          },
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: (context: TooltipItem<'scatter'>) => {
                const point = context.raw as { x: number; y: number; label: string }
                return `${point.label}: สั่งซื้อ ${point.x} ชิ้น, เฉลี่ย ${point.y} วัน`
              },
            },
          },
          legend: { display: false },
        },
        scales: {
          x: {
            title: { display: true, text: 'จำนวนที่สั่งซื้อ' },
            beginAtZero: true,
          },
          y: {
            title: { display: true, text: 'ระยะเวลาเฉลี่ยในการรับสินค้า (วัน)' },
            beginAtZero: true,
          },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างกราฟสินค้าที่เคลื่อนไหวช้าได้:', error)
  }
}

// กราฟ 3: ประสิทธิภาพของผู้จำหน่าย
const createSupplierEfficiencyChart = async () => {
  try {
    const response = await api.get<SupplierEfficiency[]>(
      '/dashboard/performance/supplier-efficiency',
    )
    const data = response.data

    const colors: Record<string, string> = {
      Excellent: '#4CAF50',
      Good: '#8BC34A',
      Average: '#FF9800',
      Poor: '#F44336',
    }

    new Chart(supplierEfficiencyChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.supplier_name),
        datasets: [
          {
            label: 'ระยะเวลาจัดส่งเฉลี่ย (วัน)',
            data: data.map((item) => item.avg_delivery_days),
            backgroundColor: data.map((item) => colors[item.efficiency_rating] || '#607D8B'),
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            bottom: 40,
          },
        },
        plugins: {
          legend: {
            display: true,
            labels: {
              generateLabels: () =>
                Object.entries(colors).map(([rating, color]) => ({
                  text: rating,
                  fillStyle: color,
                  strokeStyle: color,
                  lineWidth: 1,
                })),
            },
          },
        },
        scales: {
          y: {
            title: { display: true, text: 'จำนวนวัน' },
            beginAtZero: true,
          },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างกราฟประสิทธิภาพผู้จำหน่ายได้:', error)
  }
}

onMounted(async () => {
  await createOnTimeSuppliersChart()
  await createSlowProductsChart()
  await createSupplierEfficiencyChart()
})
</script>
