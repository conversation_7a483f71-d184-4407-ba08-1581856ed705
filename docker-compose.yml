version: '3.8'

services:
  # Service สำหรับ Back-end Application
  backend:
    build: . # สั่งให้ build จาก Dockerfile ใน directory ปัจจุบัน
    container_name: pharmacy-backend-app
    ports:
      - "8044:3000" # Map port 3000 ของเครื่อง host ไปยัง port 3000 ของ container
    volumes:
      # Map ไฟล์ SQLite จากเครื่อง host เข้าไปใน container
      - ./database2.sqlite:/usr/src/app/database2.sqlite