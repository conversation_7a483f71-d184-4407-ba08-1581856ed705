import { defineStore, acceptHMRUpdate } from 'pinia'
import {
  productService,
  productGroupService,
  specialReportGroupService,
} from 'src/services/productService'
import type { Product } from 'src/pages/Stock/Product/types/product'
import type { Supplier } from 'src/types/supplier'
import type { ProductGroup } from 'src/pages/Stock/Product/types/product-group'
import type { SpecialReportGroup } from 'src/types/special-report-group'

const defaultForm: Product = {
  id: 0,
  product_code: '',
  product_name: '',
  generic_name: '',
  standard_cost: 0,
  selling_price: 0,
  storage_location: '',
  stock_min: 0,
  stock_max: 0,
  packing_size: '',
  reg_no: '',
  manufacturer: {
    id: 0,
    supplier_number: '',
    name: '',
    address: '',
    tel: '',
    tax_number: '',
    contact_name: '',
    fax: '',
    email: '',
    type: {
      id: 0,
      name: '',
    },
    isactive: false,
    credit_days: 0,
  },
  distributor: {
    id: 0,
    supplier_number: '',
    name: '',
    address: '',
    tel: '',
    tax_number: '',
    contact_name: '',
    fax: '',
    email: '',
    type: {
      id: 0,
      name: '',
    },
    isactive: false,
    credit_days: 0,
  },
  indications: '',
  warnings: '',
  purchase_notes: '',
  cost_notes: '',
  sales_alert_message: '',
  generic_group: '',
  product_group: {
    id: 0,
    name: '',
  },
  wholesale_control_group: '',
  special_report_group: {
    id: 0,
    name: '',
  },
  unit: '',
  barcode: '',
  isactive: false,
  wholesale: false,
  wholesale1: 0,
  wholesale2: 0,
  wholesale3: 0,
  wholesale4: 0,
  wholesale5: 0,
  retail: false,
  retail1: 0,
  retail2: 0,
  retail3: 0,
  retail4: 0,
  retail5: 0,
  manufactureDate: '',
  expirationDate: '',
}

function getProductPayload(form: Product): Product {
  return {
    ...form,
    manufacturer:
      form.manufacturer && form.manufacturer.id
        ? ({
          id: form.manufacturer.id,
          supplier_number: '',
          name: '',
          address: '',
          tel: '',
          tax_number: '',
          contact_name: '',
          fax: '',
          email: '',
          type: { id: 0, name: '' },
          isactive: false,
        } as Supplier)
        : {
          id: 0,
          supplier_number: '',
          name: '',
          address: '',
          tel: '',
          tax_number: '',
          contact_name: '',
          fax: '',
          email: '',
          type: { id: 0, name: '' },
          isactive: false,
          credit_days: 0,
        },

    distributor:
      form.distributor && form.distributor.id
        ? ({
          id: form.distributor.id,
          supplier_number: '',
          name: '',
          address: '',
          tel: '',
          tax_number: '',
          contact_name: '',
          fax: '',
          email: '',
          type: { id: 0, name: '' },
          isactive: false,
        } as Supplier)
        : {
          id: 0,
          supplier_number: '',
          name: '',
          address: '',
          tel: '',
          tax_number: '',
          contact_name: '',
          fax: '',
          email: '',
          type: { id: 0, name: '' },
          isactive: false,
          credit_days: 0,
        },

    product_group:
      form.product_group && form.product_group.id
        ? ({ id: form.product_group.id, name: '' } as ProductGroup)
        : { id: 0, name: '' },
    special_report_group:
      form.special_report_group && form.special_report_group.id
        ? ({ id: form.special_report_group.id, name: '' } as SpecialReportGroup)
        : { id: 0, name: '' },
  }
}

export const useProductStore = defineStore('product', {
  state: () => ({
    form: { ...defaultForm },
    products: [] as Product[],
    productGroups: [] as ProductGroup[],
    specialReportGroups: [] as SpecialReportGroup[],
  }),

  getters: {
    totalProducts: (state): number => state.products.length,
  },

  actions: {
    async fetchProducts() {
      try {
        this.products = await productService.getProducts()
      } catch (error) {
        console.error('Error fetching products:', error)
      }
    },

    async fetchProductGroups() {
      try {
        this.productGroups = await productGroupService.getProductGroups()
      } catch (error) {
        console.error('Error fetching product groups:', error)
      }
    },

    async fetchSpecialReportGroups() {
      try {
        this.specialReportGroups = await specialReportGroupService.getSpecialReportGroup()
      } catch (error) {
        console.error('Error fetching special report group:', error)
      }
    },

    async filterProducts(search: string, filter?: string) {
      try {
        this.products = await productService.filterProducts(search, filter)
      } catch (error) {
        console.error('Error filtering products:', error)
      }
    },

    async addProduct() {
      try {
        const maxId = this.products.length > 0 ? Math.max(...this.products.map((p) => p.id)) : 0
        this.form.id = maxId + 1

        const payload = getProductPayload(this.form)
        console.log(payload)

        const newProduct = await productService.addProduct(payload)
        this.products.push(newProduct)
        this.resetForm()
      } catch (error) {
        console.error('Error adding product:', error)
      }
    },
    async updateProduct() {
      try {
        const payload = getProductPayload(this.form)
        console.log(payload)
        const updated = await productService.updateProduct(payload)
        const index = this.products.findIndex((p) => p.id === updated.id)
        if (index !== -1) this.products[index] = updated
      } catch (error) {
        console.error('Error updating product:', error)
      }
    },

    async removeProduct(productId: number) {
      try {
        await productService.deleteProduct(productId)
        this.products = this.products.filter((p) => p.id !== productId)
      } catch (error) {
        console.error('Error deleting product:', error)
      }
    },

    resetForm() {
      this.form = JSON.parse(JSON.stringify(defaultForm))
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useProductStore, import.meta.hot))
}
