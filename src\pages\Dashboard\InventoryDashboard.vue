<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">แดชบอร์ดภาพรวมคลังสินค้า</div>

    <!-- กราฟบน -->
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section style="height: 420px" class="q-pa-sm">
            <div class="text-h6 q-mb-sm text-center">สินค้าคงเหลือตามสาขา</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="branchChartRef"
                style="width: 100%; height: 100%; display: block"
              ></canvas>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section style="height: 420px" class="q-pa-sm">
            <div class="text-h6 q-mb-sm text-center">สินค้าคงเหลือสูงสุด</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="productsChartRef"
                style="width: 100%; height: 100%; display: block"
              ></canvas>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- กราฟล่าง -->
    <div class="row">
      <div class="col-12">
        <q-card>
          <q-card-section style="height: 450px; overflow: hidden" class="q-pa-sm">
            <div class="text-h6 q-mb-sm text-center">มูลค่าสินค้าตามกลุ่มสินค้า</div>
            <div style="overflow-x: auto; height: 100%; padding-bottom: 24px">
              <div style="min-width: 1000px; height: 100%; position: relative">
                <canvas ref="groupChartRef" style="height: 100%; display: block"></canvas>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import { api } from 'src/boot/axios'

Chart.register(...registerables)

interface BranchStock {
  branch_name: string
  total_remaining: number
  total_value: number
}

interface TopProduct {
  product_name: string
  total_remaining: number
}

interface GroupValue {
  group_name: string
  total_value: number
}

const branchChartRef = ref<HTMLCanvasElement>()
const productsChartRef = ref<HTMLCanvasElement>()
const groupChartRef = ref<HTMLCanvasElement>()

const createBranchChart = async () => {
  const { data } = await api.get<BranchStock[]>('/dashboard/inventory/by-branch')
  new Chart(branchChartRef.value!, {
    type: 'bar',
    data: {
      labels: data.map((d) => d.branch_name),
      datasets: [
        {
          label: 'จำนวนคงเหลือ',
          data: data.map((d) => d.total_remaining),
          backgroundColor: '#26A69A',
        },
        {
          label: 'มูลค่ารวม',
          data: data.map((d) => d.total_value),
          backgroundColor: '#AB47BC',
          yAxisID: 'y1',
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      layout: {
        padding: {
          top: 10,
          bottom: 40, // ✅ เพิ่ม padding ล่าง
        },
      },
      scales: {
        x: {
          ticks: {
            autoSkip: false,
            maxRotation: 0,
            minRotation: 0,
            font: {
              size: 12,
            },
          },
        },
        y: { beginAtZero: true },
        y1: { beginAtZero: true, position: 'right' },
      },
      plugins: {
        legend: { position: 'top' },
      },
    },
  })
}

const createProductsChart = async () => {
  const { data } = await api.get<TopProduct[]>(
    '/dashboard/inventory/top-remaining-products?limit=10',
  )
  new Chart(productsChartRef.value!, {
    type: 'doughnut',
    data: {
      labels: data.map((d) => d.product_name),
      datasets: [
        {
          data: data.map((d) => d.total_remaining),
          backgroundColor: [
            '#26A69A',
            '#AB47BC',
            '#4CAF50',
            '#FF9800',
            '#F44336',
            '#2196F3',
            '#9C27B0',
            '#607D8B',
            '#795548',
            '#E91E63',
          ],
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      layout: {
        padding: {
          top: 10,
          bottom: 40, // ✅ ป้องกันกรอบล่างตัด legend
        },
      },
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            boxWidth: 12,
            padding: 8,
          },
        },
      },
    },
  })
}

const createGroupChart = async () => {
  const { data } = await api.get<GroupValue[]>('/dashboard/inventory/value-by-group')
  new Chart(groupChartRef.value!, {
    type: 'bar',
    data: {
      labels: data.map((d) => d.group_name),
      datasets: [
        {
          label: 'มูลค่ารวม',
          data: data.map((d) => d.total_value),
          backgroundColor: '#26A69A',
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      layout: {
        padding: {
          top: 10,
          bottom: 40,
        },
      },
      scales: {
        x: {
          ticks: {
            autoSkip: false,
            maxRotation: 0,
            minRotation: 0,
            font: {
              size: 13,
            },
          },
        },
        y: { beginAtZero: true },
      },
      plugins: {
        legend: { position: 'top' },
      },
    },
  })
}

onMounted(async () => {
  await createBranchChart()
  await createProductsChart()
  await createGroupChart()
})
</script>
