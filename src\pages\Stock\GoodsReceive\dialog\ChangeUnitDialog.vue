<template>
  <q-dialog v-model="dialogChangeUnit.isOpenCU">
    <q-card style="max-width: 1000px; width: 800px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">หน่วยสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="shadow-2">
            <!-- <q-table
              flat
              class="body-table"
              :rows="stockStore.getStocksDialog"
              :columns="columns"
              row-key="id"
              hide-bottom
              style="height: 300px"
              :pagination="pagination"
              :rows-per-page-options="[]"
            >
            </q-table> -->
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center">
        <q-btn class="btn-accept" dense flat label="บันทึก" color="white" />

        <q-btn class="btn-cancel" dense flat label="ยกเลิก" color="white" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
<script setup lang="ts">
// import { date } from 'quasar'

// import { QTableColumn } from 'quasar'
import { useDialogGRDetails } from 'src/stores/dialogs/dialog-gr-details'
// import { useGoodsReceiptStore } from 'src/stores/goodsreceipt'
// import { useStockStore } from 'src/stores/stock'
// import { ref } from 'vue'
// import { computed } from 'vue'

// const store = useGoodsReceiptStore()
const dialogChangeUnit = useDialogGRDetails()

const closeDialog = () => {
  dialogChangeUnit.closeCU()
}

// const saveCU = () => {
//   dialogChangeUnit.closeCU()
// }

// const formattedDate = computed({
//   get() {
//     return date.formatDate(store.form.receive_date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
//   },
//   set(value: string) {
//     const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
//     store.form.receive_date = newDate // อัพเดทค่าใน store
//   },
// })

// const stockStore = useStockStore()

// const pagination = ref({
//   rowsPerPage: 12,
// })

// const columns = <QTableColumn[]>[
//   {
//     name: 'unit',
//     label: 'หน่วย',
//     field: 'unit',
//     align: 'left' as const,
//     sortable: true,
//   },
//   {
//     name: 'unit',
//     label: 'บรรจุ',
//     field: '-',
//     align: 'left' as const,
//     sortable: true,
//   },
//   {
//     name: 'price',
//     label: 'ราคา',
//     field: '-',
//     align: 'left' as const,
//   },
// ]
</script>
<style scoped>
.type-payment {
  margin-left: 10px;
  margin-top: 20px;
}

.gap-container {
  margin-bottom: 20px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.width-column {
  width: 190px;
}

.input-container {
  background-color: white;
  border-radius: 5px;
  width: 190px;
  padding-left: 10px;
  padding-right: 10px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
  width: 80px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
  width: 80px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.body-table {
  background-color: #deecff;
}

:deep(.q-table thead tr) {
  background-color: #294888;
  color: #deecff;
}
</style>
