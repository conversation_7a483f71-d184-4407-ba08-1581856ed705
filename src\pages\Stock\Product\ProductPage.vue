<template>
  <StockNavigation></StockNavigation>
  <div class="container q-pa-md q-ma-md page-fade-in">
    <div class="row wrap-container">
      <div class="col-8 col-md-12 col-sm-12 col-xs-12 search-container">
        <SearchComponent v-model="searchQuery" placeholder="ค้นหา" />
      </div>
      <div class="col-4 filter-button-container">
        <FilterComponent v-model="selectedFilter" :filterOptions="filterOptions" class="q-mb-md" />
        <q-btn flat @click="openDialog" class="add-button q-mb-md" label="เพิ่มรายการสินค้า" />
      </div>
    </div>
    <q-card flat class="custom-table">
      <q-table flat class="body-table" :rows="filteredProducts" :columns="columns" row-key="id" :pagination="pagination"
        :rows-per-page-options="[]" style="height: 100%; max-height: 700px">
        <template #body-cell-actions="props">
          <q-td class="q-gutter-x-sm" style="min-width: 100px">
            <q-btn icon="edit" padding="none" flat @click="openEditDialog(props.row)" style="color: #e19f62" />
            <q-btn icon="delete" padding="none" style="color: #b53638" flat
              @click="handleOpenDeleteDialog(props.row)" />
            <q-btn icon="info" padding="none" style="color: #294888" flat @click="openInfoDialog(props.row)" />
          </q-td>
        </template>
      </q-table>
    </q-card>
    <!-- Dialog ยืนยันการลบ -->
    <ConfirmDeleteProductDialog v-model="formDelete" :item="selectedRow" @confirm="handleConfirmDelete" />
    <AddProductDialog></AddProductDialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useDialogStore } from 'src/stores/dialogs/dialog-store'
import { useProductStore } from 'src/stores/inventory/product'
import type { Product } from 'src/pages/Stock/Product/types/product'
import SearchComponent from 'src/components/searchComponent.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import type { QTableColumn } from 'quasar'
import StockNavigation from '../components/StockNavigation.vue'
import ConfirmDeleteProductDialog from './dialog/confirmDeleteProductDialog.vue'
import AddProductDialog from './dialog/addProductDialog.vue'

const store = useProductStore()
const dialogStore = useDialogStore()
const formDelete = ref(false)
const selectedRow = ref<Product>(JSON.parse(JSON.stringify(store.form)))
const searchQuery = ref('')
const selectedFilter = ref<string>('')

const filteredProducts = computed(() => store.products)

watch([searchQuery, selectedFilter], async ([search, filter]) => {
  if (search.trim()) {
    await store.filterProducts(search, filter || undefined)
  } else {
    await store.fetchProducts()
  }
})

const filterOptions = [
  { label: 'บาร์โค้ด', value: 'barcode' },
  { label: 'รหัสสินค้า', value: 'product_code' },
  { label: 'ชื่อสินค้า', value: 'product_name' },
  { label: 'สถานที่เก็บ', value: 'storage_location' },
  { label: 'กลุ่มชื่อสามัญ', value: 'generic_group' },
  { label: 'ข้อความเตือน', value: 'warning' },
]

const pagination = ref({
  rowsPerPage: 12,
})

onMounted(async () => {
  try {
    await store.fetchProducts()
  } catch (error) {
    console.error('Error fetching products:', error)
  }
})

const columns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'ลำดับ',
    field: 'id',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_code',
    label: 'รหัสสินค้า',
    field: 'product_code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_name',
    label: 'ชื่อสินค้า',
    field: 'product_name',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'unit',
    label: 'หน่วยฐาน',
    field: 'unit',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'wholesale1',
    label: 'ราคาส่ง',
    field: (row) => row.wholesale1.toFixed(2),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'stock_min',
    label: 'จำนวนขั้นต่ำ',
    field: 'stock_min',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'stock_max',
    label: 'จำนวนขั้นสูง',
    field: 'stock_max',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]

// เปิด Dialog ลบ
function handleOpenDeleteDialog(row: Product) {
  selectedRow.value = { ...row }
  formDelete.value = true
}

// ยืนยันการลบ
const handleConfirmDelete = async (row: Product) => {
  try {
    await store.removeProduct(row.id)
  } catch (error) {
    console.error('Error removing product:', error)
  }
}

const openEditDialog = (row: Product) => {
  store.form = { ...row }
  dialogStore.open('edit')
}

const openInfoDialog = (row: Product) => {
  store.form = { ...row }
  dialogStore.open('view')
}

const openDialog = () => {
  dialogStore.open()
}
</script>

<style scoped>
:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 311px;
  max-width: 1042px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

@media (max-width: 1200px) {
  .wrap-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-button-container {
    flex-direction: row;
    width: 100%;
  }

  .text-right {
    text-align: left;
  }
}
</style>
