<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">ระบบจัดการสต็อก</div>

    <q-tabs v-model="activeTab" class="text-primary q-mb-md">
      <q-tab name="inventory" label="ภาพรวมสต็อก" />
      <q-tab name="goods-receipt" label="การรับสินค้า" />
      <q-tab name="transfer" label="การโอน/จ่ายสินค้า" />
      <q-tab name="purchase" label="ใบสั่งซื้อ" />
      <q-tab name="performance" label="ประสิทธิภาพ" />
    </q-tabs>

    <q-tab-panels v-model="activeTab" animated>
      <q-tab-panel name="inventory">
        <InventoryDashboard />
      </q-tab-panel>

      <q-tab-panel name="goods-receipt">
        <GoodsReceiptDashboard />
      </q-tab-panel>

      <q-tab-panel name="transfer">
        <TransferDashboard />
      </q-tab-panel>

      <q-tab-panel name="purchase">
        <PurchaseOrderDashboard />
      </q-tab-panel>

      <q-tab-panel name="performance">
        <PerformanceDashboard />
      </q-tab-panel>
    </q-tab-panels>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import InventoryDashboard from './InventoryDashboard.vue'
import GoodsReceiptDashboard from './GoodsReceiptDashboard.vue'
import TransferDashboard from './TransferDashboard.vue'
import PurchaseOrderDashboard from './PurchaseOrderDashboard.vue'
import PerformanceDashboard from './PerformanceDashboard.vue'

const activeTab = ref('inventory')
</script>
