import type { Product } from "../pages/Stock/Product/types/product";
import type { StockTransferOrder } from "./stockTransferOrder";
import type { StockTransferSlipDetails } from "./stockTransferSlipDetails";

export interface StockTransferOrderDetails {
  id: number;
  sto: StockTransferOrder;
  sts_details: StockTransferSlipDetails[];
  product: Product;
  quantity: number;
  lot_number?: string;
}

