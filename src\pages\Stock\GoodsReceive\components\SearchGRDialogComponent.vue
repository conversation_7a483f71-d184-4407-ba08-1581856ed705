<template>
  <div class="row container-search items-center q-gutter-sm full-width">
    <span class="label-text">คำที่ต้องการค้นหา</span>
    <q-input
      v-model="searchTerm"
      :placeholder="placeholder"
      borderless
      dense
      class="input-container-search"
      :input-style="{ padding: '4px 0', margin: '0', height: '100%' }"
    >
    </q-input>

    <q-icon name="search" class="search-icon" style="margin-left: 10px" />
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue'

const props = defineProps({
  modelValue: String,
  placeholder: {
    type: String,
    default: 'ค้นหา',
  },
})

const emit = defineEmits(['update:modelValue'])
const searchTerm = ref(props.modelValue)

watch(searchTerm, (newValue) => {
  emit('update:modelValue', newValue)
})
</script>

<style scoped>
.container-search {
  display: flex;
  align-items: center;
  padding: 0 10px;
  margin: 10px auto;
  border-radius: 5px;
  background-color: #294888;
  width: 100%; /* เปลี่ยนจาก 500px เป็น 100% */
}

.input-container-search {
  flex: 1;
  background-color: white;
  border-radius: 5px;
  height: 35px;
  padding: 0 10px;
  margin-bottom: 20px;
  display: flex; /* เพิ่ม */
  align-items: center; /* ให้ข้อความอยู่กลางแนวตั้ง */
}

.label-text {
  color: white;
  white-space: nowrap;
  font-size: 14px;
  margin-right: 10px;
  margin-bottom: 20px;
}

.search-icon {
  margin-left: 10px;
  font-size: 30px;
  color: gainsboro;
  margin-right: 10px;
  margin-bottom: 20px;
}
</style>
