# Use Node.js 20 LTS as base image
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Install a simple HTTP server to serve the built files
RUN npm install -g http-server

# Expose port 8045
EXPOSE 8045

# Start the application
CMD ["http-server", "dist/spa", "-p", "8045", "-a", "0.0.0.0"]
