# --- Stage 1: Build ---
# ใช้ Node.js image ที่มีเครื่องมือสำหรับ build
FROM node:20-alpine AS builder

# ตั้งค่า working directory ภายใน container
WORKDIR /usr/src/app

# คัดลอก package.json และ package-lock.json (หรือ yarn.lock)
COPY package*.json ./

# ติดตั้ง dependencies ทั้งหมดเพื่อใช้ในการ build
RUN npm install

# คัดลอกไฟล์ source code ทั้งหมด
COPY . .

# สั่ง build TypeScript project ให้เป็น JavaScript
# (คำสั่งนี้ตั้งสมมติฐานว่าใน package.json มี script "build": "tsc" หรือคล้ายกัน)
RUN npm run build

# --- Stage 2: Production ---
# ใช้ Node.js image ที่มีขนาดเล็กสำหรับ run application
FROM node:20-alpine

WORKDIR /usr/src/app

# คัดลอกเฉพาะ production dependencies จาก stage 'builder' เพื่อความรวดเร็ว
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/package*.json ./

# คัดลอกไฟล์ที่ build แล้วจาก stage 'builder'
COPY --from=builder /usr/src/app/dist ./dist

# คัดลอกโฟลเดอร์ images สำหรับ static files
COPY --from=builder /usr/src/app/images ./images

# เปิด port ที่ application ใช้งาน (จากไฟล์ server.ts ของคุณ)
EXPOSE 3000

# คำสั่งสำหรับ run application
# (เปลี่ยน 'dist/server.js' หากไฟล์ entry point ของคุณแตกต่างไป)
CMD [ "node", "dist/server.js" ]