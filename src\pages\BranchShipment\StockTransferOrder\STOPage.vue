<template>
  <BranchShipmentNavigation></BranchShipmentNavigation>
  <div class="container q-pa-md q-ma-md page-fade-in">
    <div class="row wrap-container">
      <div class="col-7 search-container">
        <SearchComponent placeholder="ค้นหา" />
      </div>
      <div class="col-2 filter-button-container">
        <FilterComponent v-model="selectedFilter" :filterOptions="filterOptions" class="q-mb-md" />
      </div>
      <div class="col-3">
        <q-btn flat @click="openDialog" class="add-button q-mb-md" label="สร้างการร้องขอสินค้ารายการใหม่" />
      </div>
    </div>

    <!-- Status Filter Component -->
    <StatusFilterComponent :status-types="statusTypes" :columns="columnsWithTemplates" :pagination="pagination"
      @row-click="handleRowClick" @status-change="handleStatusChange" />

    <AddSTODialog v-model="addSTODialogOpen" :mode="modeSTO"></AddSTODialog>
    <STODetailDialog v-model="stoDetailsDialogOpen" :mode="modeSTODetails"></STODetailDialog>
  </div>
</template>
<script setup lang="ts">
import type { QTableColumn } from 'quasar'
import StatusFilterComponent from 'src/components/StatusFilterComponent.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import SearchComponent from 'src/components/searchComponent.vue'
import { useStockTransferOrderStore } from 'src/stores/orders/stocktransferorder'
import type { StockTransferOrder } from 'src/types/stockTransferOrder'
import { onMounted, ref, computed } from 'vue'
import AddSTODialog from './dialog/addSTODialog.vue'
import STODetailDialog from './dialog/STODetailDialog.vue'
import BranchShipmentNavigation from '../components/BranchShipmentNavigation.vue'
const store = useStockTransferOrderStore()
const addSTODialogOpen = ref(false)
const stoDetailsDialogOpen = ref(false)
const modeSTO = ref('add')
const modeSTODetails = ref('edit')

onMounted(async () => {
  await store.fetchSTOByStatus()
})
const columns = <QTableColumn[]>[
  {
    name: 'code',
    label: 'เลขที่',
    field: 'code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'request_date',
    label: 'วันที่',
    field: (row) => {
      const date = new Date(row.request_date)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // เดือนต้อง +1 เพราะเริ่มจาก 0
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    },
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'source_branch',
    label: 'สาขาต้นทาง',
    field: (row) =>
      row.source_branch ? `${row.source_branch.id} : ${row.source_branch.name}` : '',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'destination_branch',
    label: 'สาขาปลายทาง',
    field: (row) =>
      row.destination_branch ? `${row.destination_branch.id} : ${row.destination_branch.name}` : '',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'user',
    label: 'พนักงาน',
    field: (row) => (row.user ? row.user.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'transfer_status',
    label: 'สถานะโอน',
    field: 'transfer_status',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'status',
    label: 'สถานะ',
    field: 'status',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]
const pagination = ref({
  rowsPerPage: 12,
})
const selectedFilter = ref<string>('')
const filterOptions = [
  { label: 'เลขที่', value: 'code' },
  { label: 'วันที่', value: 'name' },
  { label: 'สถานะโอน', value: 'supplier_number' },
  { label: 'สถานะ', value: 'supplier_number' },
]

// Status types configuration for the StatusFilterComponent
const statusTypes = computed(() => [
  {
    key: 'prepare',
    label: 'เตรียมรายการ',
    tableTitle: 'เตรียมรายการ',
    data: store.stoPrepare,
    defaultSelected: true
  },
  {
    key: 'process',
    label: 'ดำเนินรายการ',
    tableTitle: 'กำลังดำเนินการ',
    data: store.stoProcess,
    defaultSelected: true
  },
  {
    key: 'all',
    label: 'รายการทั้งหมด',
    tableTitle: 'รายการทั้งหมด',
    data: store.sto,
    defaultSelected: false
  }
])

// Columns with templates for status badges
const columnsWithTemplates = computed(() => [
  ...columns,
  // Add custom template columns if needed
])
const openDialog = () => {
  addSTODialogOpen.value = true
  modeSTO.value = 'add'
}

async function openDetailsDialog(_evt: Event, row: StockTransferOrder) {
  console.log('🚀 Clicked Row Object:', row)
  await store.fetchDetailsBySTO(row.id)
  await store.fetchSTOById(row.id)
  stoDetailsDialogOpen.value = true
  modeSTODetails.value = 'edit'
}

const handleStatusChange = (statuses: Record<string, boolean>) => {
  console.log('Status changed:', statuses)
  // You can add additional logic here if needed
}

const handleRowClick = async (event: Event, row: unknown) => {
  // Type-cast the row to StockTransferOrder since we know it's from our STO data
  await openDetailsDialog(event, row as StockTransferOrder)
}
</script>
<style scoped>
.green-card {
  background-color: #439e62;
  /* สีเขียว */
  color: white;
}

.yellow-card {
  background-color: #ed9b53;
  /* สีเหลือง */
  color: white;
}

.red-card {
  background-color: #b53638;
  /* สีแดง */
  color: white;
}

.blue-card {
  /* สีแดง */
  background-color: #83a7d8;
  color: white;
}

.purple-card {
  background-color: #ba8cc9;
  color: #000000;
}

.green-transfer-card {
  background-color: #36b54d;
  color: #000000;
}

.green-transfer-card2 {
  background-color: #42bd86;
  color: #000000;
}

.red-transfer-card {
  background-color: #f55355;
  color: #000000;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 300px;
  max-width: 1000px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

@media (max-width: 1200px) {
  .wrap-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-button-container {
    flex-direction: row;
    width: 100%;
  }

  .text-right {
    text-align: left;
  }
}
</style>
