import type { Product, ProductExpirationAlert } from 'src/pages/Stock/Product/types/product'
import type { ProductGroup } from 'src/pages/Stock/Product/types/product-group'
import type { SpecialReportGroup } from 'src/types/special-report-group'
import { api } from 'src/boot/axios'

const API_URL = '/product'
const API_ProductGroup = '/productgroup'
const API_SpecialReportGroup = '/specialreportgroup'

export const productService = {
  async getProducts(): Promise<Product[]> {
    const response = await api.get(API_URL)
    console.log('Loading data :', response.data)
    return response.data
  },

  async deleteProduct(id: number): Promise<void> {
    await api.delete(`${API_URL}/${id}`)
  },

  async addProduct(product: Product): Promise<Product> {
    const response = await api.post(API_URL, product)
    return response.data
  },

  async updateProduct(product: Product): Promise<Product> {
    const response = await api.put(`${API_URL}/${product.id}`, product)
    return response.data
  },
  async filterProducts(search: string, filter?: string): Promise<Product[]> {
    const response = await api.post(`${API_URL}/filter`, {
      search,
      filter,
    })
    return response.data
  },
  async breakProduct(
    fromProductId: number,
    toProductId: number,
    quantity: number,
    branchId: number,
  ) {
    const response = await api.post(`${API_URL}/${fromProductId}/break`, {
      toId: toProductId,
      quantity,
      branchId,
    })
    return response.data
  },

  async getExpirationAlerts(): Promise<ProductExpirationAlert[]> {
    const response = await api.get(`${API_URL}/expiration-alerts`)
    return response.data
  },
}

export const productGroupService = {
  async getProductGroups(): Promise<ProductGroup[]> {
    const response = await api.get(API_ProductGroup)
    console.log('Loading productGroup :', response.data)
    return response.data
  },
}

export const specialReportGroupService = {
  async getSpecialReportGroup(): Promise<SpecialReportGroup[]> {
    const response = await api.get(API_SpecialReportGroup)
    console.log('Loading SpecialProductGroup :', response.data)
    return response.data
  },
}
