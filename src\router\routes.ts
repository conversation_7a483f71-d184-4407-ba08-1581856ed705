import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login',
  },
  {
    path: '/home',
    component: () => import('layouts/MainLayout.vue'),
    children: [{ path: '', component: () => import('pages/IndexPage.vue') }],
    meta: { requiresAuth: true },
  },
  {
    path: '/login',
    component: () => import('layouts/EmptyLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('src/pages/LoginPage.vue'),
      },
    ],
    meta: { public: true },
  },
  {
    path: '/dashboard',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'dashboard-1',
        component: () => import('src/pages/Dashboard/DashBoardPage.vue'),
      },
      {
        path: 'chart',
        name: 'dashboard-chart',
        component: () => import('src/pages/Dashboard/DashBoardPage2.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },
  {
    path: '/stock',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'product',
        name: 'product-list',
        component: () => import('src/pages/Stock/Product/ProductPage.vue'),
      },
      {
        path: 'order',
        name: 'order-list',
        component: () => import('src/pages/Stock/PurchaseOrder/PurchaseOrderPage.vue'),
      },
      {
        path: 'receive',
        name: 'goods-receive',
        component: () => import('src/pages/Stock/GoodsReceive/GoodsReceivePage.vue'),
      },
      {
        path: 'status',
        name: 'status-order',
        component: () => import('src/pages/Stock/StatusOrder/StatusOrderPage.vue'),
      },
      {
        path: 'inventory',
        name: 'inventory',
        component: () => import('src/pages/Stock/Inventory/InventoryPage.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },
  {
    path: '/suppliers',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'supplier-list',
        component: () => import('src/pages/Supplier/SupplierPage.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },
  {
    path: '/user',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'user-dashboard',
        component: () => import('src/pages/User/userDashBoard.vue'),
      },
      {
        path: 'summary',
        name: 'user-summary',
        component: () => import('src/pages/User/userSummarypage.vue'),
      },
      {
        path: 'management',
        name: 'user-management',
        component: () => import('src/pages/User/userManagement.vue'),
      },
      {
        path: 'checkinout',
        name: 'user-checkinout',
        component: () => import('src/pages/User/checkInOut.vue'),
      },
      {
        path: 'attendance-table',
        name: 'attendance-table',
        component: () => import('src/pages/User/userAttendancetable.vue'),
      },
      {
        path: 'attendance/:id',
        name: 'user-attendance-detail',
        component: () => import('src/pages/User/userAttendanceDetail.vue'),
      },
      {
        path: 'userDetail/:id',
        name: 'user-detail',
        component: () => import('src/pages/User/userDetail.vue'),
      },
      {
        path: 'leaveRequest/:id',
        name: 'user-leave-request',
        component: () => import('src/pages/User/userLeaveRequest.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },
  {
    path: '/branch',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'sto',
        name: 'sto',
        component: () => import('src/pages/BranchShipment/StockTransferOrder/STOPage.vue'),
      },
      {
        path: 'sts',
        name: 'sts',
        component: () => import('src/pages/BranchShipment/StockTransferSlip/STSPage.vue'),
      },
      {
        path: 'br',
        name: 'br',
        component: () => import('src/pages/BranchShipment/BranchReceive/BRPage.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },
  {
    path: '/order',
    component: () => import('layouts/EmptyLayout.vue'),
    children: [
      {
        path: ':id/print/summary',
        name: 'receipt-po',
        component: () => import('src/pages/BranchShipment/ReceiptPO.vue'),
      },
    ],
    meta: { public: true },
  },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
]

export default routes
