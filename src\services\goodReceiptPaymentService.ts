import type { PaymentGoodsReceipt } from 'src/types/paymentGoodsReceipt'
import { api } from 'src/boot/axios'

const API_URL = '/payment-gr'
// เปลี่ยนให้ตรงกับ backend ของคุณ

export const goodsReceiptPaymentService = {
  // ดึงข้อมูลรายการ GoodsReceipt ทั้งหมด
  async getAll(): Promise<PaymentGoodsReceipt[]> {
    const response = await api.get(API_URL)
    return response.data
  },

  async getById(id: number): Promise<PaymentGoodsReceipt> {
    const response = await api.get(`${API_URL}/${id}`)
    return response.data
  },

  async getByGRId(id: number): Promise<PaymentGoodsReceipt[]> {
    const response = await api.get(`${API_URL}/gr/${id}`)
    return response.data
  },

  async create(grId: number, payment: PaymentGoodsReceipt): Promise<PaymentGoodsReceipt> {
    const response = await api.post(`${API_URL}/${grId}`, payment)
    return response.data
  },

  async delete(id: number): Promise<void> {
    await api.delete(`${API_URL}/${id}`)
  },
}
