<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">แดชบอร์ดการโอน/จ่ายสินค้า</div>

    <div class="row q-gutter-md">
      <!-- กราฟปริมาณการโอนตามสาขา -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">ปริมาณการโอนระหว่างสาขา</div>
            <canvas ref="branchVolumeChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- กราฟสินค้าที่ถูกจ่ายมากที่สุด -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">สินค้าที่ถูกจ่ายมากที่สุด</div>
            <canvas ref="topProductsChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- แผนที่แสดงการไหลของการโอน -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">แผนภาพการไหลของการโอน</div>
            <canvas ref="flowMapChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { TooltipItem } from 'chart.js'
import { api } from 'src/boot/axios'

Chart.register(...registerables)

const branchVolumeChartRef = ref<HTMLCanvasElement>()
const topProductsChartRef = ref<HTMLCanvasElement>()
const flowMapChartRef = ref<HTMLCanvasElement>()

// อินเทอร์เฟส
interface TransferVolume {
  source_branch: string
  destination_branch: string
  total_transferred: number
}

interface IssuedProduct {
  product_name: string
  total_transferred: number
}

interface TransferFlow {
  source_name: string
  destination_name: string
  flow_quantity: number
}

// ฟังก์ชันสร้างกราฟ
const createBranchVolumeChart = async () => {
  try {
    const response = await api.get<TransferVolume[]>('/dashboard/transfer/volume-by-branch')
    const data = response.data

    new Chart(branchVolumeChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => `${item.source_branch} → ${item.destination_branch}`),
        datasets: [
          {
            label: 'จำนวนที่โอน',
            data: data.map((item) => item.total_transferred),
            backgroundColor: '#FF9800',
          },
        ],
      },
      options: {
        responsive: true,
        indexAxis: 'y',
        scales: {
          x: { beginAtZero: true },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างกราฟปริมาณการโอนได้:', error)
  }
}

const createTopProductsChart = async () => {
  try {
    const response = await api.get<IssuedProduct[]>('/dashboard/transfer/top-issued-products')
    const data = response.data.slice(0, 10)

    new Chart(topProductsChartRef.value!, {
      type: 'doughnut',
      data: {
        labels: data.map((item) => item.product_name),
        datasets: [
          {
            data: data.map((item) => item.total_transferred),
            backgroundColor: [
              '#FF9800',
              '#4CAF50',
              '#2196F3',
              '#9C27B0',
              '#F44336',
              '#607D8B',
              '#795548',
              '#E91E63',
              '#26A69A',
              '#AB47BC',
            ],
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          legend: { position: 'bottom' },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างกราฟสินค้าที่ถูกจ่ายได้:', error)
  }
}

const createFlowMapChart = async () => {
  try {
    const response = await api.get<TransferFlow[]>('/dashboard/transfer/flow-map')
    const data = response.data

    new Chart(flowMapChartRef.value!, {
      type: 'bubble',
      data: {
        datasets: [
          {
            label: 'การไหลของการโอน',
            data: data.map((item, index) => ({
              x: index % 5,
              y: Math.floor(index / 5),
              r: Math.sqrt(item.flow_quantity) / 10,
              label: `${item.source_name} → ${item.destination_name}`,
              quantity: item.flow_quantity,
            })),
            backgroundColor: 'rgba(255, 152, 0, 0.6)',
            borderColor: '#FF9800',
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          tooltip: {
            callbacks: {
              label: (context: TooltipItem<'bubble'>) => {
                const point = context.raw as {
                  x: number
                  y: number
                  r: number
                  label: string
                  quantity: number
                }
                return `${point.label}: ${point.quantity} หน่วย`
              },
            },
          },
        },
        scales: {
          x: { display: false },
          y: { display: false },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างแผนภาพการไหลได้:', error)
  }
}

onMounted(async () => {
  await createBranchVolumeChart()
  await createTopProductsChart()
  await createFlowMapChart()
})
</script>
