<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">แดชบอร์ดใบสั่งซื้อ</div>

    <div class="row q-col-gutter-md q-mb-md">
      <!-- สรุปใบสั่งซื้อรายเดือน -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section style="height: 420px" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">สรุปใบสั่งซื้อรายเดือน</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="summaryChartRef"
                style="width: 100%; height: 100%; display: block"
              ></canvas>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- สัดส่วนการสั่งซื้อจากผู้จำหน่าย -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section style="height: 420px" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">สัดส่วนการสั่งซื้อจากผู้จำหน่าย</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="supplierRatioChartRef"
                style="width: 100%; height: 100%; display: block"
              ></canvas>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- สินค้าที่สั่งซื้อมากที่สุด -->
    <div class="row">
      <div class="col-12">
        <q-card>
          <q-card-section style="height: 450px; overflow: hidden" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">สินค้าที่สั่งซื้อมากที่สุด</div>
            <div style="overflow-x: auto; height: 100%; padding-bottom: 24px">
              <div style="min-width: 800px; height: 100%; position: relative">
                <canvas
                  ref="topProductsChartRef"
                  style="width: 100%; height: 100%; display: block"
                ></canvas>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import { api } from 'src/boot/axios'
import type { TooltipItem } from 'chart.js'

Chart.register(...registerables)

const summaryChartRef = ref<HTMLCanvasElement | null>(null)
const topProductsChartRef = ref<HTMLCanvasElement | null>(null)
const supplierRatioChartRef = ref<HTMLCanvasElement | null>(null)

interface SummaryByMonth {
  month: string
  order_count: number
  total_value: number
}

interface TopProduct {
  product_name: string
  total_ordered: number
  total_value: number
}

interface SupplierRatio {
  supplier_name: string
  percentage: number
}

// ---------- กราฟสรุปใบสั่งซื้อรายเดือน ----------
const createSummaryChart = async () => {
  try {
    const response = await api.get<SummaryByMonth[]>('/dashboard/purchase-orders/summary-by-month')
    const data = response.data

    if (!summaryChartRef.value || !Array.isArray(data)) return

    new Chart(summaryChartRef.value, {
      type: 'line',
      data: {
        labels: data.map((item) => item.month),
        datasets: [
          {
            label: 'จำนวนใบสั่งซื้อ',
            data: data.map((item) => item.order_count),
            borderColor: '#2196F3',
            backgroundColor: 'rgba(33, 150, 243, 0.1)',
            tension: 0.4,
          },
          {
            label: 'มูลค่ารวม',
            data: data.map((item) => item.total_value),
            borderColor: '#4CAF50',
            backgroundColor: 'rgba(76, 175, 80, 0.1)',
            yAxisID: 'y1',
            tension: 0.4,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            bottom: 40,
          },
        },
        scales: {
          y: { beginAtZero: true },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            beginAtZero: true,
          },
        },
        plugins: {
          legend: { position: 'top' },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างกราฟสรุปใบสั่งซื้อได้:', error)
  }
}

// ---------- กราฟสินค้าที่สั่งซื้อมากที่สุด ----------
const createTopProductsChart = async () => {
  try {
    const response = await api.get<TopProduct[]>('/dashboard/purchase-orders/top-products?limit=5')
    const data = response.data

    if (!topProductsChartRef.value || !Array.isArray(data)) return

    new Chart(topProductsChartRef.value, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.product_name),
        datasets: [
          {
            label: 'จำนวนที่สั่งซื้อ',
            data: data.map((item) => item.total_ordered),
            backgroundColor: '#9C27B0',
          },
          {
            label: 'มูลค่ารวม',
            data: data.map((item) => item.total_value),
            backgroundColor: '#E91E63',
            yAxisID: 'y1',
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            bottom: 40,
          },
        },
        scales: {
          y: { beginAtZero: true },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            beginAtZero: true,
          },
        },
        plugins: {
          legend: { position: 'top' },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างกราฟสินค้าที่สั่งซื้อมากที่สุดได้:', error)
  }
}

// ---------- กราฟสัดส่วนการสั่งซื้อจากผู้จำหน่าย ----------
const createSupplierRatioChart = async () => {
  try {
    const response = await api.get<SupplierRatio[]>('/dashboard/purchase-orders/supplier-ratio')
    const data = response.data

    if (!supplierRatioChartRef.value || !Array.isArray(data)) return

    new Chart(supplierRatioChartRef.value, {
      type: 'pie',
      data: {
        labels: data.map((item) => item.supplier_name),
        datasets: [
          {
            data: data.map((item) => item.percentage),
            backgroundColor: [
              '#26A69A',
              '#AB47BC',
              '#4CAF50',
              '#FF9800',
              '#F44336',
              '#2196F3',
              '#9C27B0',
              '#607D8B',
              '#795548',
              '#E91E63',
            ],
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            bottom: 24,
          },
        },
        plugins: {
          legend: { position: 'bottom' },
          tooltip: {
            callbacks: {
              label: (context: TooltipItem<'pie'>) => {
                return `${context.label}: ${context.parsed}%`
              },
            },
          },
        },
      },
    })
  } catch (error) {
    console.error('ไม่สามารถสร้างกราฟสัดส่วนผู้จำหน่ายได้:', error)
  }
}

onMounted(async () => {
  await createSummaryChart()
  await createTopProductsChart()
  await createSupplierRatioChart()
})
</script>
