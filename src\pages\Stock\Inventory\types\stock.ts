import type { Branch } from "../../../../types/branch"
import type { Product } from "../../Product/types/product"

export interface Stock {
    id: number
    product: Product
    status: 'สินค้าใกล้หมด' | 'สินค้าคงอยู่' | 'สินค้าหมด'
    remaining: number
    branch: Branch
}

// import type { Branch } from "./branch"
// import type { StockDetails } from "./stockDetails"

// export interface Stock {
//     id: number
//     stockDetails: StockDetails[]
//     status: 'ใกล้หมด' | 'คงอยู่' | 'หมด'
//     branch: Branch
// }   