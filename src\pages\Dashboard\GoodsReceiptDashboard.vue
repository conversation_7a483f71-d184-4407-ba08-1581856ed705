<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md text-center text-weight-medium">แดชบอร์ดการรับสินค้า</div>

    <!-- แถวบน -->
    <div class="row q-col-gutter-md q-mb-md">
      <!-- ปริมาณการรับสินค้า -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section style="height: 420px" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">ปริมาณการรับสินค้า (รายเดือน)</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="volumeChartRef"
                style="width: 100%; height: 100%; display: block"
              ></canvas>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- ผู้จำหน่ายยอดนิยม -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section style="height: 420px" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">ผู้จำหน่ายยอดนิยม</div>
            <div style="position: relative; width: 100%; height: 100%; padding-bottom: 24px">
              <canvas
                ref="suppliersChartRef"
                style="width: 100%; height: 100%; display: block"
              ></canvas>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- แถวล่าง -->
    <div class="row">
      <div class="col-12">
        <q-card>
          <q-card-section style="height: 450px; overflow: hidden" class="q-pa-sm">
            <div class="text-h6 text-center q-mb-sm">มูลค่าการรับสินค้ารายเดือน</div>
            <div style="overflow-x: auto; height: 100%; padding-bottom: 24px">
              <div style="min-width: 1000px; height: 100%; position: relative">
                <canvas ref="valueChartRef" style="height: 100%; display: block"></canvas>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import { api } from 'src/boot/axios'

Chart.register(...registerables)

// Interfaces
interface VolumeByMonth {
  month: string
  total_quantity: number
  receipt_count: number
  total_value: number
}

interface TopSupplier {
  supplier_id: number
  supplier_name: string
  receipt_count: number
  total_quantity: number
  total_value: number
}

interface ValueByMonth {
  month: string
  total_value: number
  avg_cost_price: number
  item_count: number
}

// Chart Refs
const volumeChartRef = ref<HTMLCanvasElement>()
const suppliersChartRef = ref<HTMLCanvasElement>()
const valueChartRef = ref<HTMLCanvasElement>()

const createVolumeChart = async () => {
  try {
    const response = await api.get('/dashboard/goods-receipt/volume-by-month')
    const data: VolumeByMonth[] = response.data

    new Chart(volumeChartRef.value!, {
      type: 'line',
      data: {
        labels: data.map((item) => item.month),
        datasets: [
          {
            label: 'ปริมาณ',
            data: data.map((item) => item.total_quantity),
            borderColor: '#26A69A',
            backgroundColor: 'rgba(38, 166, 154, 0.1)',
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 5,
            pointHoverRadius: 7,
          },
          {
            label: 'จำนวนใบรับ',
            data: data.map((item) => item.receipt_count),
            borderColor: '#AB47BC',
            backgroundColor: 'rgba(171, 71, 188, 0.1)',
            yAxisID: 'y1',
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 5,
            pointHoverRadius: 7,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            bottom: 40,
          },
        },
        plugins: {
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              font: { size: 12, weight: 'bold' },
            },
          },
        },
        scales: {
          y: { beginAtZero: true },
          y1: {
            beginAtZero: true,
            position: 'right',
            grid: { drawOnChartArea: false },
          },
          x: {
            ticks: { font: { size: 11 } },
          },
        },
      },
    })
  } catch (err) {
    console.error('Error loading volume chart:', err)
  }
}

const createSuppliersChart = async () => {
  try {
    const response = await api.get('/dashboard/goods-receipt/top-suppliers')
    const data: TopSupplier[] = response.data.slice(0, 10)

    new Chart(suppliersChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.supplier_name),
        datasets: [
          {
            label: 'จำนวนใบรับ',
            data: data.map((item) => item.receipt_count),
            backgroundColor: '#26A69A',
            borderRadius: 4,
            borderSkipped: false,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        indexAxis: 'y',
        layout: {
          padding: {
            bottom: 24,
          },
        },
        plugins: {
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              font: { size: 12, weight: 'bold' },
            },
          },
        },
        scales: {
          x: { beginAtZero: true },
          y: {
            ticks: { font: { size: 11 } },
            grid: { display: false },
          },
        },
      },
    })
  } catch (err) {
    console.error('Error loading suppliers chart:', err)
  }
}

const createValueChart = async () => {
  try {
    const response = await api.get('/dashboard/goods-receipt/value-by-month')
    const data: ValueByMonth[] = response.data

    new Chart(valueChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.month),
        datasets: [
          {
            label: 'มูลค่ารวม',
            data: data.map((item) => item.total_value),
            backgroundColor: '#4CAF50',
            borderRadius: 6,
            borderSkipped: false,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            bottom: 40,
          },
        },
        plugins: {
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              font: { size: 12, weight: 'bold' },
            },
          },
        },
        scales: {
          y: { beginAtZero: true },
          x: {
            ticks: {
              font: { size: 11 },
              autoSkip: false,
            },
          },
        },
      },
    })
  } catch (err) {
    console.error('Error loading value chart:', err)
  }
}

onMounted(async () => {
  await createVolumeChart()
  await createSuppliersChart()
  await createValueChart()
})
</script>
