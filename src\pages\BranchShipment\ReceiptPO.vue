<template>
  <q-page class="q-pa-xl bg-white page-fade-in" style="font-family: 'Prompt', sans-serif; font-size: 14px">
    <!-- Header -->
    <div class="row justify-between items-start q-mb-md">
      <!-- Logo and Company Name -->
      <div>
        <q-img src="icon.png/header.png" width="80px" height="80px" class="q-mb-sm" />
        <div class="text-bold">SUKTHAVORN OSOT</div>
        <div>123/45 ถนนสุขุมวิท แขวงคลองตัน เขตคลองเตย กรุงเทพฯ 10110</div>
        <div>เบอร์โทร: 012-345-6789</div>
        <div>เลขประจำตัวผู้เสียภาษี: 0105559999999</div>
      </div>

      <!-- Document Title -->
      <div class="text-right">
        <div class="text-h5 text-bold text-pink">ใบสั่งซื้อ</div>
        <div><b>เลขที่:</b> {{ store.form.code }}</div>
        <div><b>วันที่:</b> {{ formattedOrderDate }}</div>
        <div><b>ครบกำหนด:</b> {{ formattedDate }}</div>
        <div><b>ผู้สั่งซื้อ:</b> {{ store.form.user.name }}</div>
      </div>
    </div>

    <!-- Supplier Section -->
    <div class="q-mb-md">
      <div class="text-bold">ผู้จำหน่าย:</div>
      <div>{{ store.form.supplier.name }}</div>
      <div>{{ store.form.supplier.address }}</div>
    </div>

    <!-- Table of items -->
    <q-table flat bordered dense :rows="store.orderItems" :columns="columns" row-key="id" hide-bottom class="q-mb-md">
      <template v-slot:body-cell-index="props">
        <q-td :props="props">
          {{ props.rowIndex + 1 }}
        </q-td>
      </template>
      <template v-slot:body-cell-total="props">
        <q-td :props="props">
          {{ formatPrice(props.row.qty * props.row.price) }}
        </q-td>
      </template>
    </q-table>

    <!-- Totals -->
    <div class="row justify-end q-gutter-sm q-mb-lg">
      <div class="col-4">
        <div class="row justify-between">
          <div>รวมเป็นเงิน</div>
          <div>{{ store.form.tax_total }}</div>
        </div>
        <div class="row justify-between">
          <div>ส่วนลด</div>
          <div>
            {{ store.form.order_discount }}
            <span> บาท</span>
          </div>
        </div>
        <div class="row justify-between">
          <div>จำนวนเงินหลังหักส่วนลด</div>
          <div>{{ store.form.tax_total - store.form.order_discount }}</div>
        </div>
        <div class="row justify-between">
          <div>ราคาไม่รวมภาษีมูลค่าเพิ่ม</div>
          <div>{{ store.form.po_total }}</div>
        </div>
        <div class="row justify-between">
          <div>ภาษีมูลค่าเพิ่ม 7.00%</div>
          <div>{{ store.form.tax }}</div>
        </div>
        <div class="row justify-between text-bold">
          <div>จำนวนเงินรวมทั้งสิ้น</div>
          <div>{{ store.form.tax_total }}</div>
        </div>
      </div>
    </div>

    <!-- Signature area -->
    <div class="row justify-between q-mt-xl q-pt-xl">
      <div class="text-center">
        _______________________<br />
        ผู้ขาย<br />
        _______________________ <br />
        วันที่<br />
      </div>
      <div class="text-center">
        {{ store.form.user.name }}<br />
        ผู้อนุมัติ<br />
        {{ formattedCurrentDate }} <br />
        วันที่<br />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { date, type QTableColumn } from 'quasar'
import { usePurchaseOrderStore } from 'src/stores/orders/purchaseorder'
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const isOpen = ref(false)
const store = usePurchaseOrderStore()
const currentDate = ref(new Date())
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      console.log(newId)
      isOpen.value = true
      // await store.fetchPOById(newId);
    }
  },
  { immediate: true },
)

const columns = <QTableColumn[]>[
  {
    name: 'index',
    label: 'ลำดับ',
    field: '',
    align: 'left' as const,
    sortable: false,
  },
  {
    name: 'product_code',
    label: 'รหัสสินค้า',
    field: (row) => (row.product ? row.product.product_code : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_name',
    label: 'รายการสินค้า',
    field: (row) => (row.product ? row.product.product_name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'quantity',
    label: 'จำนวน',
    field: (row) => row.quantity,
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'unit',
    label: 'หน่วย',
    field: (row) => (row.product ? row.product.unit : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'unit_price',
    label: 'ราคาต่อหน่วย',
    field: (row) => row.unit_price.toFixed(2),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'total_price',
    label: 'รวมเงิน',
    field: (row) => row.total_price.toFixed(2),
    align: 'left' as const,
    sortable: true,
  },
]
const formattedCurrentDate = computed({
  get() {
    return date.formatDate(currentDate.value, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    currentDate.value = new Date(value) // แปลงจาก string กลับเป็น Date
  },
})
const formattedOrderDate = computed({
  get() {
    return date.formatDate(store.form.order_date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
    store.form.order_date = newDate // อัพเดทค่าใน store
  },
})

const formattedDate = computed({
  get() {
    return date.formatDate(store.form.date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
    store.form.date = newDate // อัพเดทค่าใน store
  },
})

function formatPrice(val: number) {
  return new Intl.NumberFormat('th-TH', { style: 'currency', currency: 'THB' }).format(val)
}
</script>
<style scoped>
.container {
  background-color: #ffffff;
  font-style: sans-serif;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  color: black;
  align-items: center;
  justify-content: left;
  display: flex;
  margin-left: 50px;
  margin-top: 40px;
}
</style>
