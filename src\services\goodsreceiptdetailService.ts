import type { GoodsReceipt } from 'src/pages/Stock/GoodsReceive/types/goodsReceipt'
import type { GoodsReceiptDetail } from 'src/pages/Stock/GoodsReceive/types/goodsReceiptDatail'
import { api } from 'src/boot/axios'

const API_URL = '/gr-details'
// เปลี่ยนให้ตรงกับ backend ของคุณ

export const goodsReceiptDetailService = {
  // ดึงข้อมูลรายการ GoodsReceipt ทั้งหมด
  async getAll(): Promise<GoodsReceipt[]> {
    const response = await api.get(API_URL)
    return response.data
  },

  // ดึงข้อมูล Purchase Order ตาม ID
  async getById(id: number): Promise<GoodsReceipt> {
    const response = await api.get(`${API_URL}/${id}`)
    return response.data
  },
  async getDetailsById(productId: number, branchId: number): Promise<GoodsReceiptDetail> {
    const response = await api.get(`${API_URL}/product/${productId}/branch/${branchId}`)
    return response.data
  },
  async getDetailsByGR(id: number): Promise<GoodsReceiptDetail[]> {
    const response = await api.get(`${API_URL}/gr/${id}`)
    return response.data
  },
  async deletePoItems(selectedIds: number[]) {
    console.log(selectedIds)
    await api.delete(`${API_URL}/${selectedIds.join(',')}`, { data: { ids: selectedIds } })
  },

  async addProduct(grDetails: GoodsReceiptDetail[], grId: number) {
    console.log(grDetails)
    await api.post(`${API_URL}/${grId}`, grDetails)
  },

  // อัปเดต Purchase Order
  async update(id: number, gr: Partial<GoodsReceipt>): Promise<GoodsReceipt> {
    const response = await api.put(`${API_URL}/${id}`, gr)
    return response.data
  },

  async updateGRDetailsFromPO(grDetails: GoodsReceiptDetail[], grId: number) {
    await api.put(`${API_URL}/${grId}`, grDetails)
  },

  // ลบ Purchase Order
  async delete(id: number): Promise<void> {
    await api.delete(`${API_URL}/${id}`)
  },

  async filter(
    search: string,
    filter: string,
    startDate: string,
    endDate: string,
  ): Promise<GoodsReceipt[]> {
    try {
      const response = await api.post(`${API_URL}/filter`, {
        search,
        filter,
        startDate,
        endDate,
      })
      return response.data
    } catch (error) {
      console.error('Error filtering purchase orders', error)
      throw error
    }
  },
}
